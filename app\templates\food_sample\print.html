<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>留样记录打印</title>
    <style nonce="{{ csp_nonce }}">
        body {
            font-family: SimSun, "宋体", "Microsoft YaHei", "微软雅黑", sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14pt;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 18pt;
            margin-bottom: 20px;
        }
        .info {
            margin-bottom: 20px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .row {
            display: flex;
            justify-content: space-between;
        }
        .col {
            width: 48%;
        }
        /* 信息表格 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #2c3e50;
            padding: 8px;
            vertical-align: top;
            font-size: 10pt;
        }

        .info-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            text-align: center;
            width: 15%;
        }

        .info-table td {
            background-color: white;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .image-container img {
            max-width: 80%;
            max-height: 300px;
            border: 1px solid #ddd;
        }
        /* 签名区域 */
        .footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            page-break-inside: avoid;
        }

        .signature {
            width: 45%;
            text-align: left;
        }

        .signature p {
            margin: 8px 0;
            font-size: 10pt;
            line-height: 1.8;
        }
        @media print {
            body {
                padding: 0;
            }
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">{{ project_name|default('智慧食堂平台') }}</div>
            <div class="subtitle">食品留样记录单</div>
        </div>

        <table class="info-table">
            <tr>
                <th>留样编号</th>
                <td>{{ food_sample.sample_number }}</td>
                <th>区域</th>
                <td>{{ food_sample.area.name }}</td>
                <th>用餐日期</th>
                <td>{{ food_sample.meal_date }}</td>
            </tr>
            <tr>
                <th>食谱名称</th>
                <td>{{ food_sample.recipe.name | clean_recipe_name }}</td>
                <th>餐次</th>
                <td>{{ food_sample.meal_type }}</td>
                <th>留样数量</th>
                <td>{{ food_sample.sample_quantity }} {{ food_sample.sample_unit }}</td>
            </tr>
            <tr>
                <th>存储位置</th>
                <td>{{ food_sample.storage_location }}</td>
                <th>存储温度</th>
                <td>{{ food_sample.storage_temperature }}</td>
                <th>状态</th>
                <td>{{ food_sample.status }}</td>
            </tr>
            <tr>
                <th>留样时间</th>
                <td>{{  food_sample.start_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                <th>留样结束时间</th>
                <td>{{  food_sample.end_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                <th>操作员</th>
                <td>{{ food_sample.operator.real_name or food_sample.operator.username }}</td>
            </tr>
        </table>

        {% if food_sample.status == '已销毁' %}
        <table class="info-table">
            <tr>
                <th>销毁时间</th>
                <td>{{  food_sample.destruction_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                <th>销毁操作员</th>
                <td>{{ food_sample.destruction_operator.real_name or food_sample.destruction_operator.username }}</td>
                <th></th>
                <td></td>
            </tr>
        </table>
        {% endif %}

        {% if food_sample.sample_image %}
        <div class="image-container">
            <h3>留样图片</h3>
            <img src="{{ url_for('static', filename=food_sample.sample_image, _external=True) }}" alt="留样图片">
        </div>
        {% endif %}

        <div class="footer">
            <div class="signature">
                <p>留样人：{{ food_sample.operator.real_name or food_sample.operator.username }}</p>
                <p>签名：________________</p>
            </div>
            {% if food_sample.status == '已销毁' %}
            <div class="signature">
                <p>销毁人：{{ food_sample.destruction_operator.real_name or food_sample.destruction_operator.username }}</p>
                <p>签名：________________</p>
            </div>
            {% else %}
            <div class="signature">
                <p>审核人：________________</p>
                <p>签名：________________</p>
            </div>
            {% endif %}
        </div>

        <div class="footer">
            <div class="signature">
                <p>食堂负责人：________________</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>打印时间：{{  food_sample.created_at|format_datetime('%Y-%m-%d %H:%M:%S')  }}</p>
            </div>
        </div>
    </div>

    <script nonce="{{ csp_nonce }}">
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
