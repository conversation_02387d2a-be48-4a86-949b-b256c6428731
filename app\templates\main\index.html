<!DOCTYPE html>
<html lang="zh-CN" data-theme="primary">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台 - 专业校园餐饮管理解决方案</title>
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">

    <!-- Bootstrap 4.6.0 CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}">
    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <!-- 主题系统 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}">

    <style>
        /* 全局样式 */
        :root {
            --hero-gradient: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
            --card-shadow: 0 10px 30px rgba(var(--theme-primary-rgb), 0.1);
            --card-hover-shadow: 0 20px 40px rgba(var(--theme-primary-rgb), 0.2);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 导航栏样式 */
        .navbar {
            background: var(--hero-gradient) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(var(--theme-primary-rgb), 0.3);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }

        /* Hero区域 */
        .hero-section {
            min-height: 100vh;
            background: var(--hero-gradient);
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            animation: fadeInUp 1s ease-out;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .btn-hero {
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            border: none;
            margin: 0 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-hero-primary {
            background: white;
            color: var(--theme-primary);
            box-shadow: 0 8px 25px rgba(255,255,255,0.3);
        }

        .btn-hero-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255,255,255,0.4);
            color: var(--theme-primary);
            text-decoration: none;
        }

        .btn-hero-outline {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-hero-outline:hover {
            background: white;
            color: var(--theme-primary);
            transform: translateY(-3px);
            text-decoration: none;
        }

        /* 浮动元素 */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1;
        }

        .floating-icon {
            position: absolute;
            color: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 60%; left: 85%; animation-delay: 2s; }
        .floating-icon:nth-child(3) { top: 80%; left: 20%; animation-delay: 4s; }
        .floating-icon:nth-child(4) { top: 30%; left: 80%; animation-delay: 1s; }
        .floating-icon:nth-child(5) { top: 70%; left: 60%; animation-delay: 3s; }
        /* 特色功能区域 */
        .features-section {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            height: 100%;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--card-hover-shadow);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--hero-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .feature-desc {
            color: #666;
            line-height: 1.6;
        }

        /* 视频教学区域 */
        .videos-section {
            padding: 100px 0;
            background: white;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 800;
            color: #333;
            margin-bottom: 20px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 60px;
        }

        .video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            margin-bottom: 30px;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--card-hover-shadow);
        }

        .video-thumbnail {
            position: relative;
            height: 200px;
            background: var(--hero-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            overflow: hidden;
            border-radius: 15px 15px 0 0;
        }

        .video-info {
            padding: 20px;
        }

        .video-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .video-desc {
            color: #666;
            font-size: 0.9rem;
        }

        /* 分页控制器样式 */
        .video-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 40px;
            gap: 15px;
        }

        .pagination-btn {
            background: var(--hero-gradient);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(var(--theme-primary-rgb), 0.3);
        }

        .pagination-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(var(--theme-primary-rgb), 0.4);
        }

        .pagination-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .pagination-info {
            background: white;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: var(--card-shadow);
            font-weight: 600;
            color: #333;
        }

        /* 视频卡片动画 */
        .video-card {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .video-card.show {
            opacity: 1;
            transform: translateY(0);
        }

        .video-card.hide {
            opacity: 0;
            transform: translateY(-20px);
        }

        /* 加载动画 */
        .videos-loading {
            text-align: center;
            padding: 60px 0;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(var(--theme-primary-rgb), 0.1);
            border-left: 4px solid var(--theme-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .btn-hero {
                display: block;
                margin: 10px 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <i class="fas fa-utensils mr-2"></i>
                智慧食堂平台
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">核心功能</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#videos">视频教学</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.guest_login') }}" title="无需注册，直接体验">
                            <i class="fas fa-user-friends mr-1"></i>体验系统
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero区域 -->
    <section class="hero-section" id="home">
        <div class="floating-elements">
            <i class="fas fa-utensils floating-icon" style="font-size: 3rem;"></i>
            <i class="fas fa-leaf floating-icon" style="font-size: 2.5rem;"></i>
            <i class="fas fa-chart-line floating-icon" style="font-size: 2rem;"></i>
            <i class="fas fa-shield-alt floating-icon" style="font-size: 2.8rem;"></i>
            <i class="fas fa-users floating-icon" style="font-size: 2.3rem;"></i>
        </div>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">智慧食堂平台</h1>
                        <p class="hero-subtitle">
                            专业的校园餐饮管理解决方案<br>
                            实现食品安全可视化、可管控、可追溯
                        </p>
                        <div class="hero-buttons mt-4">
                            <a href="{{ url_for('auth.guest_login') }}" class="btn-hero btn-hero-primary">
                                <i class="fas fa-play mr-2"></i>立即体验
                            </a>
                            <a href="#features" class="btn-hero btn-hero-outline">
                                <i class="fas fa-info-circle mr-2"></i>了解更多
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image text-center">
                        <i class="fas fa-laptop-code" style="font-size: 15rem; color: rgba(255,255,255,0.2); animation: pulse 3s infinite;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 特色功能区域 -->
    <section class="features-section" id="features">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">核心功能</h2>
                    <p class="section-subtitle">全方位的食堂管理解决方案</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <h3 class="feature-title">仓库管理</h3>
                        <p class="feature-desc">智能化仓库管理系统，实时监控库存状态，自动预警补货，确保食材供应充足。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3 class="feature-title">采购管理</h3>
                        <p class="feature-desc">规范化采购流程，供应商管理，价格对比分析，降低采购成本，提高采购效率。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">食品安全</h3>
                        <p class="feature-desc">全程食品安全追溯，从原料到餐桌的完整记录，确保食品安全可控可查。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">数据分析</h3>
                        <p class="feature-desc">智能数据分析，生成各类报表，为管理决策提供科学依据。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">人员管理</h3>
                        <p class="feature-desc">完善的权限管理体系，多角色协同工作，提高管理效率。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">移动端支持</h3>
                        <p class="feature-desc">响应式设计，支持手机、平板等移动设备，随时随地管理食堂。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 视频教学区域 -->
    <section class="videos-section" id="videos">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">视频教学</h2>
                    <p class="section-subtitle">快速上手，轻松掌握系统操作</p>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="videos-loading" id="videosLoading">
                <div class="loading-spinner"></div>
                <p>正在加载视频教学内容...</p>
            </div>

            <!-- 视频内容容器 -->
            <div class="row" id="videosContainer" style="display: none;">
                <!-- 视频内容将通过JavaScript动态加载 -->
            </div>

            <!-- 分页控制器 -->
            <div class="video-pagination" id="videoPagination" style="display: none;">
                <button class="pagination-btn" id="prevBtn" onclick="changePage(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="pagination-info" id="paginationInfo">
                    第 1 页 / 共 1 页
                </div>
                <button class="pagination-btn" id="nextBtn" onclick="changePage(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Bootstrap 4.6.0 JS -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <!-- 主题切换器 -->
    <script src="{{ url_for('static', filename='js/theme-switcher.js') }}"></script>

    <script>
        // 全局变量
        let allVideos = [];
        let currentPage = 1;
        const videosPerPage = 3; // 每页显示3个视频
        let totalPages = 1;

        // 页面加载完成后执行
        $(document).ready(function() {
            // 导航栏滚动效果
            $(window).scroll(function() {
                if ($(this).scrollTop() > 50) {
                    $('.navbar').addClass('scrolled');
                } else {
                    $('.navbar').removeClass('scrolled');
                }
            });

            // 平滑滚动
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 70
                    }, 1000);
                }
            });

            // 加载视频数据
            loadVideos();
        });

        // 加载视频教学内容
        function loadVideos() {
            console.log('开始加载视频...');
            $('#videosLoading').show();
            $('#videosContainer').hide();
            $('#videoPagination').hide();

            $.ajax({
                url: '/api/guide/videos/all',
                method: 'GET',
                success: function(response) {
                    console.log('API响应:', response);
                    $('#videosLoading').hide();

                    if (response.success && response.videos && response.videos.length > 0) {
                        console.log('找到视频数量:', response.videos.length);
                        allVideos = response.videos;
                        totalPages = Math.ceil(allVideos.length / videosPerPage);
                        currentPage = 1;
                        displayCurrentPage();
                        updatePagination();
                        $('#videosContainer').show();
                        $('#videoPagination').show();
                    } else {
                        console.log('没有找到视频或响应失败');
                        displayNoVideos();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', status, error);
                    console.error('响应状态:', xhr.status);
                    console.error('响应文本:', xhr.responseText);
                    $('#videosLoading').hide();
                    displayNoVideos();
                }
            });
        }

        // 显示当前页的视频
        function displayCurrentPage() {
            const startIndex = (currentPage - 1) * videosPerPage;
            const endIndex = startIndex + videosPerPage;
            const currentVideos = allVideos.slice(startIndex, endIndex);

            // 先隐藏所有视频卡片
            $('.video-card').addClass('hide');

            setTimeout(() => {
                displayVideos(currentVideos);

                // 显示新的视频卡片
                setTimeout(() => {
                    $('.video-card').addClass('show');
                }, 100);
            }, 300);
        }

        // 显示视频列表
        function displayVideos(videos) {
            var html = '';
            videos.forEach(function(video, index) {
                // 构建视频URL - 使用正确的字段名
                const videoUrl = video.url || video.file_path || '';
                const videoTitle = video.name || '未命名视频';
                const videoDesc = video.description || '暂无描述';
                const videoDuration = video.duration || '未知时长';
                const thumbnail = video.thumbnail || '';

                html += `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="video-card" onclick="playVideo('${videoUrl}', '${videoTitle}')">
                            <div class="video-thumbnail">
                                ${thumbnail && thumbnail !== '/static/images/video_thumbnails/default.jpg' ?
                                    `<img src="${thumbnail}" alt="${videoTitle}" style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0;">
                                     <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 3rem; z-index: 2; text-shadow: 0 2px 4px rgba(0,0,0,0.5);">
                                         <i class="fas fa-play-circle"></i>
                                     </div>` :
                                    `<i class="fas fa-play-circle"></i>`
                                }
                            </div>
                            <div class="video-info">
                                <h4 class="video-title">${videoTitle}</h4>
                                <p class="video-desc">${videoDesc}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock mr-1"></i>
                                    ${videoDuration}
                                    <span class="ml-2">
                                        <i class="fas fa-calendar mr-1"></i>
                                        ${video.created_at ? video.created_at.split(' ')[0] : ''}
                                    </span>
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });
            $('#videosContainer').html(html);
        }

        // 更新分页信息
        function updatePagination() {
            $('#paginationInfo').text(`第 ${currentPage} 页 / 共 ${totalPages} 页`);

            // 更新按钮状态
            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
        }

        // 翻页函数
        function changePage(direction) {
            const newPage = currentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                displayCurrentPage();
                updatePagination();

                // 滚动到视频区域
                $('html, body').animate({
                    scrollTop: $('#videos').offset().top - 100
                }, 500);
            }
        }

        // 显示无视频内容
        function displayNoVideos() {
            $('#videosContainer').show();
            $('#videoPagination').hide();

            var html = `
                <div class="col-12 text-center">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3 class="feature-title">视频教学</h3>
                        <p class="feature-desc">
                            视频教学内容正在制作中，敬请期待！<br>
                            您可以先体验系统的各项功能。
                        </p>
                        <a href="{{ url_for('auth.guest_login') }}" class="btn btn-primary btn-lg mt-3">
                            <i class="fas fa-play mr-2"></i>立即体验系统
                        </a>
                    </div>
                </div>
            `;
            $('#videosContainer').html(html);
        }

        // 播放视频
        function playVideo(videoUrl, title) {
            if (!videoUrl) {
                alert('视频链接不可用');
                return;
            }

            // 确保视频URL是完整的
            let fullVideoUrl = videoUrl;
            if (videoUrl.startsWith('/static/')) {
                fullVideoUrl = window.location.origin + videoUrl;
            }

            // 创建模态框播放视频
            var modal = `
                <div class="modal fade" id="videoModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-play-circle mr-2"></i>${title}
                                </h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body p-0">
                                <div class="embed-responsive embed-responsive-16by9">
                                    <video class="embed-responsive-item" controls preload="metadata">
                                        <source src="${fullVideoUrl}" type="video/mp4">
                                        <source src="${fullVideoUrl}" type="video/webm">
                                        <source src="${fullVideoUrl}" type="video/ogg">
                                        您的浏览器不支持视频播放。
                                    </video>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    如果视频无法播放，请检查网络连接或联系管理员
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#videoModal').remove();

            // 添加新模态框并显示
            $('body').append(modal);
            $('#videoModal').modal('show');

            // 模态框关闭时暂停视频
            $('#videoModal').on('hidden.bs.modal', function() {
                const video = $(this).find('video')[0];
                if (video) {
                    video.pause();
                    video.currentTime = 0;
                }
                $(this).remove();
            });

            // 视频加载错误处理
            $('#videoModal video').on('error', function() {
                $(this).parent().html(`
                    <div class="text-center p-5">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">视频加载失败</h5>
                        <p class="text-muted">视频文件可能不存在或格式不支持</p>
                        <small class="text-muted">视频路径: ${fullVideoUrl}</small>
                    </div>
                `);
            });
        }
    </script>
</body>
</html>