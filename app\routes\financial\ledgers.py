"""
明细账和总账管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import AccountingSubject, FinancialVoucher, VoucherDetail
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text, func
from datetime import datetime, date, timedelta
from collections import defaultdict


@financial_bp.route('/ledgers/detail')
@login_required
@school_required
@check_permission('财务报表', 'view')
def detail_ledger(user_area):
    """明细账管理 - 生成和查看明细账"""

    # 获取查询参数
    year = request.args.get('year', date.today().year, type=int)
    month = request.args.get('month', date.today().month, type=int)
    subject_id = request.args.get('subject_id', type=int)
    action = request.args.get('action', 'view')  # view/generate

    # 获取会计科目列表（包括系统科目和学校科目）
    subjects = AccountingSubject.query.filter(
        db.or_(
            AccountingSubject.is_system == 1,  # 系统科目
            AccountingSubject.area_id == user_area.id  # 学校科目
        ),
        AccountingSubject.is_active == True
    ).order_by(AccountingSubject.code).all()

    # 明细账数据
    ledger_data = None
    selected_subject = None
    generation_status = None

    if subject_id:
        selected_subject = AccountingSubject.query.filter(
            AccountingSubject.id == subject_id,
            db.or_(
                AccountingSubject.is_system == 1,  # 系统科目
                AccountingSubject.area_id == user_area.id  # 学校科目
            )
        ).first()

        if selected_subject:
            if action == 'generate':
                # 生成明细账
                generation_status = generate_detail_ledger(user_area.id, subject_id, year, month)

            # 获取明细账数据
            ledger_data = get_detail_ledger_data(user_area.id, subject_id, year, month)

    return render_template('financial/ledgers/detail.html',
                         subjects=subjects,
                         selected_subject=selected_subject,
                         ledger_data=ledger_data,
                         generation_status=generation_status,
                         subject_id=subject_id,
                         year=year,
                         month=month,
                         user_area=user_area)


@financial_bp.route('/ledgers/detail/generate', methods=['POST'])
@login_required
@school_required
@check_permission('财务报表', 'create')
def generate_detail_ledger_api(user_area):
    """API：生成明细账"""
    try:
        # 获取请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': '请求数据为空'}), 400

        subject_id = request_data.get('subject_id')
        year = request_data.get('year')
        month = request_data.get('month')

        # 类型转换
        if subject_id is not None:
            subject_id = int(subject_id)
        if year is not None:
            year = int(year)
        if month is not None:
            month = int(month)

        if not all([subject_id, year, month]):
            return jsonify({'success': False, 'message': '参数不完整'}), 400

    # 检查科目是否存在（包括系统科目和学校科目）
    subject = AccountingSubject.query.filter(
        AccountingSubject.id == subject_id,
        db.or_(
            AccountingSubject.is_system == 1,  # 系统科目
            AccountingSubject.area_id == user_area.id  # 学校科目
        )
    ).first()

    if not subject:
        return jsonify({'success': False, 'message': '科目不存在'}), 404

    try:
        result = generate_detail_ledger(user_area.id, subject_id, year, month)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@financial_bp.route('/ledgers/detail/batch-generate', methods=['POST'])
@login_required
@school_required
@check_permission('财务报表', 'create')
def batch_generate_detail_ledgers(user_area):
    """批量生成明细账"""
    try:
        # 获取请求数据
        request_data = request.get_json()
        if not request_data:
            current_app.logger.warning("批量生成明细账: 请求数据为空")
            return jsonify({'success': False, 'message': '请求数据为空'}), 400

        # 记录请求信息
        current_app.logger.info(f"批量生成明细账请求: user_area={user_area.id}, request_data={request_data}")

        year = request_data.get('year')
        month = request_data.get('month')
        subject_ids = request_data.get('subject_ids', [])

        # 类型转换
        if year is not None:
            year = int(year)
        if month is not None:
            month = int(month)

        if not all([year, month]):
            current_app.logger.warning(f"批量生成明细账参数不完整: year={year}, month={month}")
            return jsonify({'success': False, 'message': '年月参数不完整'}), 400

        results = []
        success_count = 0

        # 如果没有指定科目，则为所有有发生额的科目生成明细账
        if not subject_ids:
            current_app.logger.info(f"获取有发生额的科目: area_id={user_area.id}, year={year}, month={month}")
            subject_ids = get_subjects_with_transactions(user_area.id, year, month)
            current_app.logger.info(f"找到有发生额的科目数量: {len(subject_ids)}")

        if not subject_ids:
            return jsonify({
                'success': False,
                'message': f'{year}年{month}月没有找到有发生额的科目，无法生成明细账',
                'results': []
            })

        for subject_id in subject_ids:
            try:
                current_app.logger.info(f"生成明细账: subject_id={subject_id}")
                result = generate_detail_ledger(user_area.id, subject_id, year, month)
                if result['success']:
                    success_count += 1
                results.append({
                    'subject_id': subject_id,
                    'result': result
                })
            except Exception as e:
                current_app.logger.error(f"生成明细账失败: subject_id={subject_id}, error={str(e)}")
                results.append({
                    'subject_id': subject_id,
                    'result': {'success': False, 'message': str(e)}
                })

        response_data = {
            'success': True,
            'message': f'批量生成完成，成功 {success_count} 个科目',
            'results': results
        }
        current_app.logger.info(f"批量生成明细账完成: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        current_app.logger.error(f"批量生成明细账异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'批量生成失败: {str(e)}'
        }), 500


@financial_bp.route('/ledgers/general')
@login_required
@school_required
@check_permission('财务报表', 'view')
def general_ledger(user_area):
    """总账查询 - 将查询条件区域一行显示"""

    # 获取查询参数 - 将查询条件区域一行显示
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    subject_type = request.args.get('subject_type', '')
    subject_code = request.args.get('subject_code', '').strip()

    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        end_date_obj = date.today()
        start_date_obj = end_date_obj.replace(day=1)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = end_date_obj.strftime('%Y-%m-%d')

    # 使用高效的原生SQL获取总账汇总数据
    general_ledger_data = get_general_ledger_summary_optimized(
        user_area.id, start_date_obj, end_date_obj, subject_type, subject_code
    )

    return render_template('financial/ledgers/general.html',
                         general_ledger_data=general_ledger_data,
                         start_date=start_date,
                         end_date=end_date,
                         subject_type=subject_type,
                         subject_code=subject_code,
                         user_area=user_area)


@financial_bp.route('/ledgers/balance')
@login_required
@school_required
@check_permission('财务报表', 'view')
def balance_sheet_detail(user_area):
    """科目余额表"""
    
    # 获取查询参数
    balance_date = request.args.get('balance_date', date.today().strftime('%Y-%m-%d'))
    subject_type = request.args.get('subject_type', '')
    
    try:
        balance_date_obj = datetime.strptime(balance_date, '%Y-%m-%d').date()
    except ValueError:
        balance_date_obj = date.today()
        balance_date = balance_date_obj.strftime('%Y-%m-%d')
    
    # 获取科目余额数据
    balance_data = get_subject_balances(user_area.id, balance_date_obj, subject_type)
    
    return render_template('financial/ledgers/balance.html',
                         balance_data=balance_data,
                         balance_date=balance_date,
                         subject_type=subject_type,
                         user_area=user_area)


def calculate_opening_balance(area_id, subject_id, start_date):
    """计算期初余额"""
    try:
        # 使用字符串格式化避免参数绑定问题
        sql = text(f"""
            SELECT
                ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                ISNULL(SUM(vd.credit_amount), 0) as total_credit
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = {area_id}
            AND vd.subject_id = {subject_id}
            AND fv.voucher_date < '{start_date}'
            AND fv.status IN ('已审核', '已记账')
        """)

        result = db.session.execute(sql).fetchone()

        if result:
            total_debit = float(result.total_debit or 0)
            total_credit = float(result.total_credit or 0)
            return total_debit - total_credit

        return 0

    except Exception as e:
        current_app.logger.error(f"计算期初余额失败: {str(e)}")
        return 0


def get_subject_detail_records(area_id, subject_id, start_date, end_date):
    """获取科目明细记录"""
    try:
        sql = text(f"""
            SELECT
                fv.voucher_date,
                fv.voucher_number,
                fv.voucher_type,
                vd.summary,
                vd.debit_amount,
                vd.credit_amount,
                vd.auxiliary_info,
                fv.id as voucher_id
            FROM voucher_details vd
            INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
            WHERE fv.area_id = {area_id}
            AND vd.subject_id = {subject_id}
            AND fv.voucher_date >= '{start_date}'
            AND fv.voucher_date <= '{end_date}'
            AND fv.status IN ('已审核', '已记账')
            ORDER BY fv.voucher_date, fv.voucher_number, vd.line_number
        """)

        results = db.session.execute(sql).fetchall()

        # 计算累计余额
        records = []
        running_balance = calculate_opening_balance(area_id, subject_id, start_date)

        for row in results:
            debit_amount = float(row.debit_amount or 0)
            credit_amount = float(row.credit_amount or 0)
            running_balance += debit_amount - credit_amount

            records.append({
                'voucher_date': row.voucher_date,
                'voucher_number': row.voucher_number,
                'voucher_type': row.voucher_type,
                'summary': row.summary,
                'debit_amount': debit_amount,
                'credit_amount': credit_amount,
                'balance': running_balance,
                'auxiliary_info': row.auxiliary_info,
                'voucher_id': row.voucher_id
            })

        return records

    except Exception as e:
        current_app.logger.error(f"获取科目明细记录失败: {str(e)}")
        return []


def get_general_ledger_summary_optimized(area_id, start_date, end_date, subject_type='', subject_code=''):
    """获取总账汇总数据 - 优化版本，使用字符串格式化避免参数绑定问题"""
    try:
        # 构建条件子句
        additional_conditions = ""
        if subject_type:
            additional_conditions += f" AND s.subject_type = '{subject_type}'"
        if subject_code:
            additional_conditions += f" AND s.code LIKE '%{subject_code}%'"

        # 使用字符串格式化构建SQL，避免参数绑定问题
        base_sql = f"""
            WITH subject_summary AS (
                SELECT
                    s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level,
                    -- 期初余额
                    ISNULL(opening.opening_balance, 0) as opening_balance,
                    -- 本期借方发生额
                    ISNULL(period.period_debit, 0) as period_debit,
                    -- 本期贷方发生额
                    ISNULL(period.period_credit, 0) as period_credit
                FROM accounting_subjects s
                LEFT JOIN (
                    SELECT vd.subject_id,
                           SUM(vd.debit_amount - vd.credit_amount) as opening_balance
                    FROM voucher_details vd
                    INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                    WHERE fv.area_id = {area_id}
                    AND fv.voucher_date < '{start_date}'
                    AND fv.status IN ('已审核', '已记账')
                    GROUP BY vd.subject_id
                ) opening ON s.id = opening.subject_id
                LEFT JOIN (
                    SELECT vd.subject_id,
                           SUM(vd.debit_amount) as period_debit,
                           SUM(vd.credit_amount) as period_credit
                    FROM voucher_details vd
                    INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                    WHERE fv.area_id = {area_id}
                    AND fv.voucher_date >= '{start_date}'
                    AND fv.voucher_date <= '{end_date}'
                    AND fv.status IN ('已审核', '已记账')
                    GROUP BY vd.subject_id
                ) period ON s.id = period.subject_id
                WHERE (s.is_system = 1 OR s.area_id = {area_id}) AND s.is_active = 1{additional_conditions}
            )
            SELECT id, code, name, subject_type, balance_direction, level,
                   opening_balance, period_debit, period_credit,
                   (opening_balance + period_debit - period_credit) as ending_balance
            FROM subject_summary
            WHERE (opening_balance != 0 OR period_debit != 0 OR period_credit != 0)
            ORDER BY code
        """

        results = db.session.execute(text(base_sql)).fetchall()

        summary_data = []
        for row in results:
            summary_data.append({
                'id': row.id,
                'code': row.code,
                'name': row.name,
                'subject_type': row.subject_type,
                'balance_direction': row.balance_direction,
                'level': row.level,
                'opening_balance': float(row.opening_balance or 0),
                'period_debit': float(row.period_debit or 0),
                'period_credit': float(row.period_credit or 0),
                'ending_balance': float(row.ending_balance or 0)
            })

        return summary_data

    except Exception as e:
        current_app.logger.error(f"获取总账汇总数据失败: {str(e)}")
        return []


def get_general_ledger_summary(area_id, start_date, end_date, subject_type=''):
    """获取总账汇总数据 - 保持向后兼容"""
    return get_general_ledger_summary_optimized(area_id, start_date, end_date, subject_type)


def get_subject_balances(area_id, balance_date, subject_type=''):
    """获取科目余额数据"""
    try:
        # 分步查询，避免复杂的SQL参数绑定问题

        # 1. 获取科目列表（包括系统科目和学校科目）
        subjects_query = AccountingSubject.query.filter(
            db.or_(
                AccountingSubject.is_system == 1,  # 系统科目
                AccountingSubject.area_id == area_id  # 学校科目
            ),
            AccountingSubject.is_active == True
        )

        if subject_type:
            subjects_query = subjects_query.filter_by(subject_type=subject_type)

        subjects = subjects_query.order_by(AccountingSubject.code).all()

        balance_data = []

        for subject in subjects:
            # 2. 计算累计发生额
            balance_sql = text(f"""
                SELECT
                    ISNULL(SUM(vd.debit_amount), 0) as total_debit,
                    ISNULL(SUM(vd.credit_amount), 0) as total_credit
                FROM voucher_details vd
                INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE fv.area_id = {area_id}
                AND vd.subject_id = {subject.id}
                AND fv.voucher_date <= '{balance_date}'
                AND fv.status IN ('已审核', '已记账')
            """)

            balance_result = db.session.execute(balance_sql).fetchone()

            total_debit = float(balance_result.total_debit or 0) if balance_result else 0
            total_credit = float(balance_result.total_credit or 0) if balance_result else 0
            balance = total_debit - total_credit

            # 根据余额方向调整显示
            display_balance = balance
            if subject.balance_direction == '贷方' and balance < 0:
                display_balance = abs(balance)
            elif subject.balance_direction == '借方' and balance < 0:
                display_balance = 0  # 借方科目不应该有贷方余额

            # 只包含有余额或有发生额的科目
            if total_debit != 0 or total_credit != 0 or balance != 0:
                balance_data.append({
                    'id': subject.id,
                    'code': subject.code,
                    'name': subject.name,
                    'subject_type': subject.subject_type,
                    'balance_direction': subject.balance_direction,
                    'level': subject.level,
                    'parent_id': subject.parent_id,
                    'total_debit': total_debit,
                    'total_credit': total_credit,
                    'balance': display_balance
                })

        return balance_data

    except Exception as e:
        current_app.logger.error(f"获取科目余额数据失败: {str(e)}")
        return []


def generate_detail_ledger(area_id, subject_id, year, month):
    """生成指定科目的月度明细账"""
    try:
        # 检查是否已经生成过
        existing_ledger = check_existing_ledger(area_id, subject_id, year, month)
        if existing_ledger:
            return {
                'success': False,
                'message': f'{year}年{month}月明细账已存在，如需重新生成请先删除现有记录'
            }

        # 获取科目信息（包括系统科目和学校科目）
        subject = AccountingSubject.query.filter(
            AccountingSubject.id == subject_id,
            db.or_(
                AccountingSubject.is_system == 1,  # 系统科目
                AccountingSubject.area_id == area_id  # 学校科目
            )
        ).first()

        if not subject:
            return {'success': False, 'message': '科目不存在'}

        # 计算期间范围
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)

        # 计算期初余额
        opening_balance = calculate_opening_balance(area_id, subject_id, start_date)

        # 获取期间内的凭证明细
        period_details = get_subject_detail_records(area_id, subject_id, start_date, end_date)

        # 生成明细账记录
        ledger_records = []
        running_balance = opening_balance
        line_number = 1

        # 添加期初余额行
        ledger_records.append({
            'line_number': line_number,
            'voucher_date': start_date,
            'voucher_number': '',
            'summary': '期初余额',
            'debit_amount': 0,
            'credit_amount': 0,
            'balance': running_balance,
            'is_opening': True
        })
        line_number += 1

        # 添加期间发生额
        for detail in period_details:
            debit_amount = detail['debit_amount']
            credit_amount = detail['credit_amount']
            running_balance += debit_amount - credit_amount

            ledger_records.append({
                'line_number': line_number,
                'voucher_date': detail['voucher_date'],
                'voucher_number': detail['voucher_number'],
                'summary': detail['summary'],
                'debit_amount': debit_amount,
                'credit_amount': credit_amount,
                'balance': running_balance,
                'voucher_id': detail['voucher_id'],
                'is_opening': False
            })
            line_number += 1

        # 添加期末余额行
        if period_details:  # 只有有发生额时才添加期末余额行
            ledger_records.append({
                'line_number': line_number,
                'voucher_date': end_date,
                'voucher_number': '',
                'summary': '期末余额',
                'debit_amount': 0,
                'credit_amount': 0,
                'balance': running_balance,
                'is_closing': True
            })

        # 保存明细账到数据库（可选，如果需要持久化）
        save_result = save_detail_ledger_to_db(area_id, subject_id, year, month, ledger_records)

        return {
            'success': True,
            'message': f'成功生成 {subject.code}-{subject.name} {year}年{month}月明细账',
            'records_count': len(ledger_records),
            'opening_balance': opening_balance,
            'closing_balance': running_balance,
            'save_result': save_result
        }

    except Exception as e:
        return {
            'success': False,
            'message': f'生成明细账失败: {str(e)}'
        }


def get_detail_ledger_data(area_id, subject_id, year, month):
    """获取明细账数据"""
    try:
        # 首先尝试从数据库获取已生成的明细账
        saved_ledger = get_saved_ledger_from_db(area_id, subject_id, year, month)
        if saved_ledger:
            return saved_ledger

        # 如果没有保存的明细账，则实时生成
        result = generate_detail_ledger(area_id, subject_id, year, month)
        if result['success']:
            # 重新获取生成的数据
            return get_saved_ledger_from_db(area_id, subject_id, year, month)
        else:
            return None

    except Exception as e:
        return None


def get_subjects_with_transactions(area_id, year, month):
    """获取指定月份有发生额的科目ID列表"""
    start_date = date(year, month, 1)
    if month == 12:
        end_date = date(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = date(year, month + 1, 1) - timedelta(days=1)

    sql = text(f"""
        SELECT DISTINCT vd.subject_id
        FROM voucher_details vd
        INNER JOIN financial_vouchers fv ON vd.voucher_id = fv.id
        WHERE fv.area_id = {area_id}
        AND fv.voucher_date >= '{start_date}'
        AND fv.voucher_date <= '{end_date}'
        AND fv.status IN ('已审核', '已记账')
        AND (vd.debit_amount > 0 OR vd.credit_amount > 0)
    """)

    try:
        results = db.session.execute(sql).fetchall()
        return [row.subject_id for row in results]
    except Exception as e:
        current_app.logger.error(f"获取有发生额科目失败: {str(e)}")
        return []


def check_existing_ledger(area_id, subject_id, year, month):
    """检查明细账是否已存在"""
    # 这里可以检查数据库中是否已有明细账记录
    # 简化处理，返回False表示不存在
    # 参数用于未来扩展
    _ = (area_id, subject_id, year, month)
    return False


def save_detail_ledger_to_db(area_id, subject_id, year, month, ledger_records):
    """保存明细账到数据库（可选功能）"""
    # 这里可以将明细账记录保存到专门的明细账表中
    # 简化处理，返回成功状态
    # 参数用于未来扩展
    _ = (area_id, subject_id, year, month, ledger_records)
    return {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}


def get_saved_ledger_from_db(area_id, subject_id, year, month):
    """从数据库获取已保存的明细账"""
    # 实时生成明细账数据
    start_date = date(year, month, 1)
    if month == 12:
        end_date = date(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = date(year, month + 1, 1) - timedelta(days=1)

    # 获取科目信息（包括系统科目和学校科目）
    subject = AccountingSubject.query.filter(
        AccountingSubject.id == subject_id,
        db.or_(
            AccountingSubject.is_system == 1,  # 系统科目
            AccountingSubject.area_id == area_id  # 学校科目
        )
    ).first()

    if not subject:
        return None

    # 计算期初余额
    opening_balance = calculate_opening_balance(area_id, subject_id, start_date)

    # 获取期间明细
    period_details = get_subject_detail_records(area_id, subject_id, start_date, end_date)

    # 构建明细账数据
    ledger_records = []
    running_balance = opening_balance
    line_number = 1

    # 期初余额
    ledger_records.append({
        'line_number': line_number,
        'voucher_date': start_date,
        'voucher_number': '',
        'summary': '期初余额',
        'debit_amount': 0,
        'credit_amount': 0,
        'balance': running_balance,
        'is_opening': True
    })
    line_number += 1

    # 期间发生额
    for detail in period_details:
        debit_amount = detail['debit_amount']
        credit_amount = detail['credit_amount']
        running_balance += debit_amount - credit_amount

        ledger_records.append({
            'line_number': line_number,
            'voucher_date': detail['voucher_date'],
            'voucher_number': detail['voucher_number'],
            'summary': detail['summary'],
            'debit_amount': debit_amount,
            'credit_amount': credit_amount,
            'balance': running_balance,
            'voucher_id': detail.get('voucher_id'),
            'is_opening': False
        })
        line_number += 1

    # 期末余额
    if period_details:
        ledger_records.append({
            'line_number': line_number,
            'voucher_date': end_date,
            'voucher_number': '',
            'summary': '期末余额',
            'debit_amount': 0,
            'credit_amount': 0,
            'balance': running_balance,
            'is_closing': True
        })

    return {
        'subject': subject,
        'year': year,
        'month': month,
        'opening_balance': opening_balance,
        'closing_balance': running_balance,
        'records': ledger_records,
        'total_debit': sum(r['debit_amount'] for r in ledger_records),
        'total_credit': sum(r['credit_amount'] for r in ledger_records),
        'transaction_count': len([r for r in ledger_records if not r.get('is_opening') and not r.get('is_closing')])
    }
