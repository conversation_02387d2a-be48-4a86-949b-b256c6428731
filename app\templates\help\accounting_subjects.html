{% extends "base.html" %}

{% block title %}会计科目管理帮助{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
<style>
/* 用友帮助页面专用样式 */
.uf-help-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 16px;
}

.uf-breadcrumb {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 8px 12px;
    margin-bottom: 16px;
    font-size: var(--uf-font-size);
}

.uf-breadcrumb a {
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-breadcrumb a:hover {
    text-decoration: underline;
}

.uf-breadcrumb-separator {
    margin: 0 6px;
    color: var(--uf-muted);
}

.uf-page-header {
    background: linear-gradient(135deg, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    padding: 20px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 16px;
    text-align: center;
    box-shadow: var(--uf-box-shadow);
}

.uf-page-header h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.uf-page-header p {
    font-size: var(--uf-font-size-large);
    margin: 0;
    opacity: 0.9;
}

.uf-quick-actions {
    background: linear-gradient(135deg, var(--uf-success) 0%, #155724 100%);
    color: white;
    padding: 16px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 16px;
    text-align: center;
    box-shadow: var(--uf-box-shadow);
}

.uf-quick-actions h5 {
    font-size: var(--uf-font-size-large);
    font-weight: 600;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.uf-quick-actions .uf-btn-group {
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.uf-quick-actions .uf-btn {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
    color: white;
    font-weight: 500;
}

.uf-quick-actions .uf-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="uf-help-container">
    <!-- 面包屑导航 -->
    <div class="uf-breadcrumb">
        <a href="{{ url_for('help.index') }}">帮助中心</a>
        <span class="uf-breadcrumb-separator">></span>
        <a href="{{ url_for('help.financial_help') }}">财务管理</a>
        <span class="uf-breadcrumb-separator">></span>
        <span>会计科目管理</span>
    </div>

    <!-- 页面标题 -->
    <div class="uf-page-header">
        <h1><i class="fas fa-chart-line"></i> 会计科目管理帮助</h1>
        <p>完整的会计科目管理指南和故障排除</p>
    </div>

    <!-- 快速操作 -->
    <div class="uf-quick-actions">
        <h5><i class="fas fa-rocket"></i> 快速操作</h5>
        <div class="uf-btn-group">
            <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn">
                <i class="fas fa-list-alt"></i> 会计科目列表
            </a>
            <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn">
                <i class="fas fa-tools"></i> 会计科目管理
            </a>
            <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn">
                <i class="fas fa-file-invoice"></i> 测试凭证功能
            </a>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div style="display: flex; gap: 16px; align-items: flex-start;">
        <!-- 使用指南 -->
        <div style="flex: 2;">
            <div class="uf-card">
                <div class="uf-card-header">
                    <div class="uf-card-header-title">
                        <i class="fas fa-graduation-cap uf-card-header-icon"></i>
                        <span>使用指南</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <div style="display: flex; gap: 20px;">
                        <div style="flex: 1;">
                            <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 4px;">
                                <i class="fas fa-route"></i> 操作步骤
                            </h6>
                            <ol style="margin: 0; padding-left: 20px; line-height: 1.6;">
                                <li style="margin-bottom: 8px;"><strong>检查状态</strong> - 查看当前会计科目情况</li>
                                <li style="margin-bottom: 8px;"><strong>选择方案</strong> - 根据提示选择合适的操作</li>
                                <li style="margin-bottom: 8px;"><strong>执行操作</strong> - 点击相应按钮执行</li>
                                <li style="margin-bottom: 8px;"><strong>验证结果</strong> - 测试API和凭证功能</li>
                            </ol>
                        </div>
                        <div style="flex: 1;">
                            <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 4px;">
                                <i class="fas fa-info-circle"></i> 科目类型
                            </h6>
                            <div style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                                <span style="background: var(--uf-primary); color: white; padding: 2px 8px; border-radius: 2px; font-size: var(--uf-font-size-small); font-weight: 500;">系统科目</span>
                                <span style="font-size: var(--uf-font-size);">标准化、全局共享、不可修改</span>
                            </div>
                            <div style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                                <span style="background: var(--uf-warning); color: #333; padding: 2px 8px; border-radius: 2px; font-size: var(--uf-font-size-small); font-weight: 500;">学校科目</span>
                                <span style="font-size: var(--uf-font-size);">学校专属、可自定义、基于系统科目</span>
                            </div>
                            <div style="background: #e8f5e8; border: 1px solid #c3e6c3; border-radius: var(--uf-border-radius); padding: 8px; margin-top: 12px;">
                                <small style="color: var(--uf-success); font-size: var(--uf-font-size-small);">
                                    <i class="fas fa-thumbs-up"></i> <strong>推荐：</strong>优先使用系统科目，特殊需求时再创建学校科目
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 常见问题 -->
            <div class="uf-card" style="margin-top: 16px;">
                <div class="uf-card-header" style="background: linear-gradient(to bottom, #fff3cd 0%, #ffeaa7 100%); color: #856404;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-question-circle" style="color: #856404;"></i>
                        <span>常见问题与解决方案</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <!-- FAQ 1 -->
                    <div class="uf-card" style="margin-bottom: 12px;">
                        <div class="uf-card-header" style="background: #fff3cd; padding: 8px 12px; cursor: pointer;" onclick="toggleFaq('faq1')">
                            <div style="display: flex; align-items: center; gap: 8px; color: #856404;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span style="font-weight: 500;">凭证编辑页面的会计科目下拉框为空怎么办？</span>
                                <i class="fas fa-chevron-down" id="faq1-icon" style="margin-left: auto; transition: transform 0.3s;"></i>
                            </div>
                        </div>
                        <div class="uf-card-body" id="faq1-content" style="display: block;">
                            <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: var(--uf-border-radius); padding: 8px; margin-bottom: 12px;">
                                <strong style="color: #0c5460;">原因：</strong>系统中没有可用的会计科目数据
                            </div>
                            <strong style="color: var(--uf-primary);">解决步骤：</strong>
                            <ol style="margin: 8px 0 0 20px; line-height: 1.6;">
                                <li>打开<a href="{{ url_for('financial.accounting_subjects_index') }}" style="color: var(--uf-primary);">会计科目管理</a></li>
                                <li>点击"检查状态"查看当前情况</li>
                                <li>如果系统科目数量为0，点击"初始化系统科目"或"修复数据结构"</li>
                                <li>等待操作完成，确认系统科目数量大于0</li>
                                <li>点击"测试API"验证科目数据正常</li>
                                <li>回到凭证编辑页面重新测试</li>
                            </ol>
                        </div>
                    </div>

                    <!-- FAQ 2 -->
                    <div class="uf-card" style="margin-bottom: 12px;">
                        <div class="uf-card-header" style="background: #d1ecf1; padding: 8px 12px; cursor: pointer;" onclick="toggleFaq('faq2')">
                            <div style="display: flex; align-items: center; gap: 8px; color: #0c5460;">
                                <i class="fas fa-cog"></i>
                                <span style="font-weight: 500;">系统科目数量为0，但有学校科目怎么办？</span>
                                <i class="fas fa-chevron-down" id="faq2-icon" style="margin-left: auto; transition: transform 0.3s;"></i>
                            </div>
                        </div>
                        <div class="uf-card-body" id="faq2-content" style="display: none;">
                            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: var(--uf-border-radius); padding: 8px; margin-bottom: 12px;">
                                <strong style="color: #721c24;">问题：</strong>这是数据结构错误，需要修复
                            </div>
                            <strong style="color: var(--uf-primary);">解决方案：</strong>
                            <ol style="margin: 8px 0 0 20px; line-height: 1.6;">
                                <li>打开<a href="{{ url_for('financial.accounting_subjects_index') }}" style="color: var(--uf-primary);">会计科目管理</a></li>
                                <li>点击红色的"修复数据结构"按钮</li>
                                <li>确认操作（会重置所有会计科目数据）</li>
                                <li>等待修复完成</li>
                                <li>验证系统科目数量变为36个</li>
                            </ol>
                            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: var(--uf-border-radius); padding: 8px; margin-top: 12px;">
                                <small style="color: #856404;">
                                    <i class="fas fa-exclamation-triangle"></i> 注意：此操作会删除现有科目数据，请确保没有重要凭证依赖
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ 3 -->
                    <div class="uf-card" style="margin-bottom: 12px;">
                        <div class="uf-card-header" style="background: #d4edda; padding: 8px 12px; cursor: pointer;" onclick="toggleFaq('faq3')">
                            <div style="display: flex; align-items: center; gap: 8px; color: #155724;">
                                <i class="fas fa-plus"></i>
                                <span style="font-weight: 500;">如何添加自定义的会计科目？</span>
                                <i class="fas fa-chevron-down" id="faq3-icon" style="margin-left: auto; transition: transform 0.3s;"></i>
                            </div>
                        </div>
                        <div class="uf-card-body" id="faq3-content" style="display: none;">
                            <strong style="color: var(--uf-primary);">方法一：复制系统科目后修改</strong>
                            <ol style="margin: 8px 0 12px 20px; line-height: 1.6;">
                                <li>确保系统科目已初始化</li>
                                <li>在管理器中点击"从系统复制"按钮</li>
                                <li>访问<a href="{{ url_for('financial.accounting_subjects_index') }}" target="_blank" style="color: var(--uf-primary);">会计科目管理页面</a></li>
                                <li>编辑或添加自定义科目</li>
                            </ol>
                            <strong style="color: var(--uf-primary);">方法二：直接创建</strong>
                            <ol style="margin: 8px 0 0 20px; line-height: 1.6;">
                                <li>访问<a href="{{ url_for('financial.create_accounting_subject') }}" target="_blank" style="color: var(--uf-primary);">创建会计科目页面</a></li>
                                <li>填写科目信息并保存</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div style="flex: 1; min-width: 280px;">
            <!-- 快速链接 -->
            <div class="uf-card" style="margin-bottom: 16px;">
                <div class="uf-card-header" style="background: var(--uf-muted); color: white;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-link" style="color: white;"></i>
                        <span>快速链接</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-list-alt"></i> 会计科目列表
                        </a>
                        <a href="{{ url_for('financial.create_accounting_subject') }}" class="uf-btn uf-btn-success uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-plus"></i> 新增科目
                        </a>
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn uf-btn-warning uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-tools"></i> 会计科目管理
                        </a>
                        <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn uf-btn-info uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-file-invoice"></i> 财务凭证
                        </a>
                    </div>
                </div>
            </div>

            <!-- 相关帮助 -->
            <div class="uf-card">
                <div class="uf-card-header" style="background: var(--uf-info); color: white;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-book" style="color: white;"></i>
                        <span>相关帮助</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <a href="{{ url_for('help.financial_help') }}" style="color: var(--uf-primary); text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 4px 0;">
                            <i class="fas fa-calculator" style="color: var(--uf-primary); width: 16px;"></i>
                            <span>财务管理总览</span>
                        </a>
                        <a href="{{ url_for('help.troubleshooting') }}" style="color: var(--uf-primary); text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 4px 0;">
                            <i class="fas fa-exclamation-triangle" style="color: var(--uf-warning); width: 16px;"></i>
                            <span>故障排除</span>
                        </a>
                        <a href="{{ url_for('help.index') }}" style="color: var(--uf-primary); text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 4px 0;">
                            <i class="fas fa-home" style="color: var(--uf-success); width: 16px;"></i>
                            <span>帮助中心首页</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// FAQ 折叠功能
function toggleFaq(faqId) {
    const content = document.getElementById(faqId + '-content');
    const icon = document.getElementById(faqId + '-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.style.transform = 'rotate(180deg)';
    } else {
        content.style.display = 'none';
        icon.style.transform = 'rotate(0deg)';
    }
}

// 页面加载时默认展开第一个FAQ
document.addEventListener('DOMContentLoaded', function() {
    const firstIcon = document.getElementById('faq1-icon');
    if (firstIcon) {
        firstIcon.style.transform = 'rotate(180deg)';
    }
});
</script>
{% endblock %}
