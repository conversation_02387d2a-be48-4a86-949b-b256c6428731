{% extends "base.html" %}

{% block title %}帮助中心{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
<style>
/* 用友帮助中心专用样式 */
.uf-help-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 16px;
}

.uf-hero-section {
    background: linear-gradient(135deg, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    padding: 40px 20px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 20px;
    text-align: center;
    box-shadow: var(--uf-box-shadow);
}

.uf-hero-title {
    font-size: 32px;
    font-weight: 600;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.uf-hero-subtitle {
    font-size: var(--uf-font-size-large);
    margin: 0 0 24px 0;
    opacity: 0.9;
}

.uf-search-container {
    max-width: 500px;
    margin: 0 auto;
    display: flex;
    gap: 0;
    border-radius: var(--uf-border-radius);
    overflow: hidden;
    box-shadow: var(--uf-box-shadow);
}

.uf-search-input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    font-size: var(--uf-font-size-large);
    font-family: var(--uf-font-family);
    outline: none;
}

.uf-search-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--uf-transition);
}

.uf-search-btn:hover {
    background: rgba(255,255,255,0.3);
}

.uf-quick-access {
    background: linear-gradient(135deg, var(--uf-success) 0%, #155724 100%);
    color: white;
    padding: 20px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 20px;
    text-align: center;
    box-shadow: var(--uf-box-shadow);
}

.uf-quick-access h5 {
    font-size: var(--uf-font-size-large);
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.uf-quick-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

.uf-quick-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 16px;
    border-radius: var(--uf-border-radius);
    text-decoration: none;
    font-size: var(--uf-font-size);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--uf-transition);
}

.uf-quick-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
    text-decoration: none;
}

.uf-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.uf-help-item {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 12px;
    margin-bottom: 8px;
    transition: var(--uf-transition);
    box-shadow: var(--uf-box-shadow);
}

.uf-help-item:hover {
    border-color: var(--uf-primary);
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-help-item h6 {
    font-size: var(--uf-font-size-large);
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--uf-primary);
    display: flex;
    align-items: center;
    gap: 6px;
}

.uf-help-item p {
    font-size: var(--uf-font-size);
    color: var(--uf-muted);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

/* 修复Bootstrap text-shadow导致的字体模糊问题 */
.uf-help-item:hover,
.uf-help-item:hover *,
.uf-card:hover,
.uf-card:hover *,
.uf-btn:hover,
.uf-btn:hover *,
.form-control:-moz-focusring,
*:-moz-focusring {
    text-shadow: none !important;
}

/* 确保所有悬停状态下的文字清晰 */
*:hover {
    text-shadow: none !important;
}
</style>
{% endblock %}

{% block content %}
<div class="uf-help-container">
    <!-- Hero Section -->
    <div class="uf-hero-section">
        <h1 class="uf-hero-title">
            <i class="fas fa-life-ring"></i> 帮助中心
        </h1>
        <p class="uf-hero-subtitle">校园餐智慧食堂平台使用指南与技术支持</p>

        <!-- 搜索框 -->
        <div class="uf-search-container">
            <input type="text" class="uf-search-input" id="helpSearch"
                   placeholder="🔍 搜索帮助内容..." onkeyup="searchHelp()">
            <button class="uf-search-btn" type="button">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- 快速访问 -->
    <div class="uf-quick-access">
        <h5><i class="fas fa-rocket"></i> 快速访问</h5>
        <div class="uf-quick-buttons">
            <a href="{{ url_for('help.financial_help') }}" class="uf-quick-btn">
                <i class="fas fa-calculator"></i> 财务管理
            </a>
            <a href="{{ url_for('help.daily_help') }}" class="uf-quick-btn">
                <i class="fas fa-calendar-day"></i> 日常管理
            </a>
            <a href="{{ url_for('help.supply_help') }}" class="uf-quick-btn">
                <i class="fas fa-truck"></i> 供应链管理
            </a>
            <a href="{{ url_for('help.system_help') }}" class="uf-quick-btn">
                <i class="fas fa-cogs"></i> 系统设置
            </a>
            <a href="{{ url_for('help.troubleshooting') }}" class="uf-quick-btn">
                <i class="fas fa-exclamation-triangle"></i> 故障排除
            </a>
        </div>
    </div>

    <!-- 快速访问 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5><i class="fas fa-rocket"></i> 快速访问</h5>
                    <div class="mt-3">
                        <a href="{{ url_for('help.financial_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-calculator"></i> 财务管理
                        </a>
                        <a href="{{ url_for('help.daily_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-calendar-day"></i> 日常管理
                        </a>
                        <a href="{{ url_for('help.supply_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-truck"></i> 供应链管理
                        </a>
                        <a href="{{ url_for('help.system_help') }}" class="btn btn-outline-light me-2 mb-2">
                            <i class="fas fa-cogs"></i> 系统设置
                        </a>
                        <a href="{{ url_for('help.troubleshooting') }}" class="btn btn-outline-light mb-2">
                            <i class="fas fa-exclamation-triangle"></i> 故障排除
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要帮助内容 -->
    <div class="uf-help-grid">
        <!-- 财务管理 -->
        <div class="uf-card">
            <div class="uf-card-header" style="background: var(--uf-primary); color: white;">
                <div class="uf-card-header-title">
                    <i class="fas fa-calculator" style="color: white;"></i>
                    <span>财务管理</span>
                </div>
            </div>
            <div class="uf-card-body">
                <div class="uf-help-item">
                    <h6><i class="fas fa-chart-line" style="color: var(--uf-primary);"></i> 会计科目管理</h6>
                    <p>管理系统会计科目和学校自定义科目</p>
                    <div style="display: flex; gap: 8px;">
                        <a href="{{ url_for('help.accounting_subjects_help') }}" class="uf-btn uf-btn-primary uf-btn-sm">
                            <i class="fas fa-book"></i> 详细指南
                        </a>
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn uf-btn-success uf-btn-sm">
                            <i class="fas fa-external-link-alt"></i> 打开功能
                        </a>
                    </div>
                </div>

                <div class="uf-help-item">
                    <h6><i class="fas fa-file-invoice" style="color: var(--uf-success);"></i> 财务凭证</h6>
                    <p>创建、编辑和管理财务凭证</p>
                    <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn uf-btn-success uf-btn-sm">
                        <i class="fas fa-external-link-alt"></i> 凭证管理
                    </a>
                </div>

                <div class="uf-help-item">
                    <h6><i class="fas fa-book" style="color: var(--uf-info);"></i> 财务报表</h6>
                    <p>查看资产负债表、利润表等财务报表</p>
                    <a href="{{ url_for('financial.reports_index') }}" class="uf-btn uf-btn-info uf-btn-sm">
                        <i class="fas fa-external-link-alt"></i> 财务报表
                    </a>
                </div>

                <div style="text-align: center; margin-top: 16px;">
                    <a href="{{ url_for('help.financial_help') }}" class="uf-btn uf-btn-primary">
                        <i class="fas fa-arrow-right"></i> 查看更多财务帮助
                    </a>
                </div>
            </div>
        </div>

        <!-- 日常管理 -->
        <div class="uf-card">
            <div class="uf-card-header" style="background: var(--uf-success); color: white;">
                <div class="uf-card-header-title">
                    <i class="fas fa-calendar-day" style="color: white;"></i>
                    <span>日常管理</span>
                </div>
            </div>
            <div class="uf-card-body">
                <div class="uf-help-item">
                    <h6><i class="fas fa-utensils" style="color: var(--uf-primary);"></i> 每日菜单</h6>
                    <p>管理每日菜单和营养搭配</p>
                    <a href="{{ url_for('consumption_plan.index') }}" class="uf-btn uf-btn-primary uf-btn-sm">
                        <i class="fas fa-external-link-alt"></i> 菜单管理
                    </a>
                </div>

                <div class="uf-help-item">
                    <h6><i class="fas fa-search" style="color: var(--uf-success);"></i> 食品检查</h6>
                    <p>食品安全检查和质量监控</p>
                    <a href="{{ url_for('inspection.index') }}" class="uf-btn uf-btn-success uf-btn-sm">
                        <i class="fas fa-external-link-alt"></i> 检查管理
                    </a>
                </div>

                <div class="uf-help-item">
                    <h6><i class="fas fa-camera" style="color: var(--uf-info);"></i> 留样管理</h6>
                    <p>食品留样记录和管理</p>
                    <a href="{{ url_for('food_sample.index') }}" class="uf-btn uf-btn-info uf-btn-sm">
                        <i class="fas fa-external-link-alt"></i> 留样记录
                    </a>
                </div>

                <div style="text-align: center; margin-top: 16px;">
                    <a href="{{ url_for('help.daily_help') }}" class="uf-btn uf-btn-success">
                        <i class="fas fa-arrow-right"></i> 查看更多日常管理帮助
                    </a>
                </div>
            </div>
        </div>

        <!-- 供应链管理 -->
        <div class="uf-card">
            <div class="uf-card-header" style="background: var(--uf-info); color: white;">
                <div class="uf-card-header-title">
                    <i class="fas fa-truck" style="color: white;"></i>
                    <span>供应链管理</span>
                </div>
            </div>
            <div class="uf-card-body">
                <div class="uf-help-item">
                    <h6><i class="fas fa-shopping-cart" style="color: var(--uf-primary);"></i> 采购订单</h6>
                    <p>创建和管理采购订单</p>
                    <a href="{{ url_for('purchase_order.index') }}" class="uf-btn uf-btn-primary uf-btn-sm">
                        <i class="fas fa-external-link-alt"></i> 采购管理
                    </a>
                </div>

                <div class="uf-help-item">
                    <h6><i class="fas fa-warehouse" style="color: var(--uf-success);"></i> 库存管理</h6>
                    <p>入库、出库和库存查询</p>
                    <a href="{{ url_for('inventory.index') }}" class="uf-btn uf-btn-success uf-btn-sm">
                        <i class="fas fa-external-link-alt"></i> 库存查询
                    </a>
                </div>

                <div style="text-align: center; margin-top: 16px;">
                    <a href="{{ url_for('help.supply_help') }}" class="uf-btn uf-btn-info">
                        <i class="fas fa-arrow-right"></i> 查看更多供应链帮助
                    </a>
                </div>
            </div>
        </div>

        <!-- 故障排除 -->
        <div class="uf-card">
            <div class="uf-card-header" style="background: var(--uf-danger); color: white;">
                <div class="uf-card-header-title">
                    <i class="fas fa-exclamation-triangle" style="color: white;"></i>
                    <span>故障排除</span>
                </div>
            </div>
            <div class="uf-card-body">
                <div class="uf-help-item">
                    <h6><i class="fas fa-chart-line" style="color: var(--uf-danger);"></i> 会计科目问题</h6>
                    <p>凭证明细无法添加、科目下拉框为空</p>
                    <a href="{{ url_for('help.accounting_subjects_help') }}" class="uf-btn uf-btn-danger uf-btn-sm">
                        <i class="fas fa-wrench"></i> 解决方案
                    </a>
                </div>

                <div class="uf-help-item">
                    <h6><i class="fas fa-sign-in-alt" style="color: var(--uf-warning);"></i> 登录权限问题</h6>
                    <p>无法登录或权限不足</p>
                    <a href="{{ url_for('help.troubleshooting') }}" class="uf-btn uf-btn-warning uf-btn-sm">
                        <i class="fas fa-key"></i> 权限指南
                    </a>
                </div>

                <div style="text-align: center; margin-top: 16px;">
                    <a href="{{ url_for('help.troubleshooting') }}" class="uf-btn uf-btn-danger">
                        <i class="fas fa-arrow-right"></i> 查看更多故障排除
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 联系支持 -->
    <div class="uf-card" style="background: var(--uf-light); border: 1px solid var(--uf-border);">
        <div class="uf-card-body" style="text-align: center;">
            <h5 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; justify-content: center; gap: 8px;">
                <i class="fas fa-headset"></i> 需要更多帮助？
            </h5>
            <p style="color: var(--uf-muted); margin-bottom: 20px;">如果您在使用过程中遇到其他问题，可以通过以下方式获取支持：</p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; max-width: 800px; margin: 0 auto;">
                <div class="uf-card" style="border: 2px solid var(--uf-primary);">
                    <div class="uf-card-body" style="text-align: center;">
                        <i class="fas fa-phone" style="color: var(--uf-primary); font-size: 32px; margin-bottom: 8px;"></i>
                        <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 4px;">技术支持热线</h6>
                        <p style="color: var(--uf-muted); margin: 0;">18373062333</p>
                    </div>
                </div>
                <div class="uf-card" style="border: 2px solid var(--uf-success);">
                    <div class="uf-card-body" style="text-align: center;">
                        <i class="fas fa-envelope" style="color: var(--uf-success); font-size: 32px; margin-bottom: 8px;"></i>
                        <h6 style="color: var(--uf-success); font-weight: 600; margin-bottom: 4px;">邮件支持</h6>
                        <p style="color: var(--uf-muted); margin: 0;"><EMAIL></p>
                    </div>
                </div>
                <div class="uf-card" style="border: 2px solid var(--uf-info);">
                    <div class="uf-card-body" style="text-align: center;">
                        <i class="fas fa-comments" style="color: var(--uf-info); font-size: 32px; margin-bottom: 8px;"></i>
                        <h6 style="color: var(--uf-info); font-weight: 600; margin-bottom: 4px;">在线客服</h6>
                        <p style="color: var(--uf-muted); margin: 0;">工作日 9:00-18:00</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 用友风格搜索功能
function searchHelp() {
    const searchTerm = document.getElementById('helpSearch').value.toLowerCase();
    const helpItems = document.querySelectorAll('.uf-help-item');

    helpItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            item.style.display = 'block';
            item.style.backgroundColor = searchTerm ? '#fff3cd' : '';
            item.style.border = searchTerm ? '1px solid #ffc107' : '';
        } else {
            item.style.display = searchTerm ? 'none' : 'block';
            item.style.backgroundColor = '';
            item.style.border = '';
        }
    });
}

// 回车键搜索
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('helpSearch');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchHelp();
            }
        });
    }
});
</script>
{% endblock %}
