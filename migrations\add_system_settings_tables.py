"""
添加系统设置相关表的迁移脚本
"""

from app import create_app, db
from datetime import datetime
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration():
    """执行迁移"""
    app = create_app()
    with app.app_context():
        try:
            # 检查表是否已存在
            from sqlalchemy import inspect
            inspector = inspect(db.engine)

            # 创建系统设置表
            if 'system_settings' not in inspector.get_table_names():
                logger.info("创建系统设置表...")
                db.engine.execute("""
                CREATE TABLE system_settings (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    [key] NVARCHAR(50) NOT NULL UNIQUE,
                    value NVARCHAR(MAX) NULL,
                    description NVARCHAR(200) NULL,
                    setting_category NVARCHAR(50) NULL,
                    created_at DATETIME2 DEFAULT GETDATE() NOT NULL,
                    updated_at DATETIME2 DEFAULT GETDATE() NOT NULL,
                    created_by INT NULL,
                    updated_by INT NULL,
                    FOREIGN KEY (created_by) REFERENCES users(id),
                    FOREIGN KEY (updated_by) REFERENCES users(id)
                )
                """)
                logger.info("系统设置表创建成功")

                # 添加默认设置
                logger.info("添加默认系统设置...")
                db.engine.execute("""
                INSERT INTO system_settings ([key], value, description, setting_category, created_at, updated_at)
                VALUES
                ('project_name', N'智慧食堂平台', N'项目名称', N'基本设置', GETDATE(), GETDATE()),
                ('items_per_page', N'10', N'每页显示条目数', N'基本设置', GETDATE(), GETDATE()),
                ('system_logo', N'', N'系统Logo路径', N'基本设置', GETDATE(), GETDATE()),
                ('theme_color', N'primary', N'主题颜色', N'显示设置', GETDATE(), GETDATE()),
                ('show_welcome_message', N'1', N'显示欢迎消息', N'显示设置', GETDATE(), GETDATE()),
                ('password_min_length', N'6', N'密码最小长度', N'安全设置', GETDATE(), GETDATE()),
                ('password_complexity', N'0', N'密码复杂度要求', N'安全设置', GETDATE(), GETDATE()),
                ('session_timeout', N'7', N'会话超时时间（天）', N'安全设置', GETDATE(), GETDATE())
                """)
                logger.info("默认系统设置添加成功")
            else:
                logger.info("系统设置表已存在，跳过创建")

            # 创建数据库备份记录表
            if 'database_backups' not in inspector.get_table_names():
                logger.info("创建数据库备份记录表...")
                db.engine.execute("""
                CREATE TABLE database_backups (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    filename NVARCHAR(200) NOT NULL,
                    backup_type NVARCHAR(20) NOT NULL,
                    size INT NULL,
                    description NVARCHAR(500) NULL,
                    status NVARCHAR(20) NOT NULL DEFAULT N'成功',
                    created_at DATETIME2 DEFAULT GETDATE() NOT NULL,
                    created_by INT NULL,
                    FOREIGN KEY (created_by) REFERENCES users(id)
                )
                """)
                logger.info("数据库备份记录表创建成功")
            else:
                logger.info("数据库备份记录表已存在，跳过创建")

            # 创建系统日志表
            if 'system_logs' not in inspector.get_table_names():
                logger.info("创建系统日志表...")
                db.engine.execute("""
                CREATE TABLE system_logs (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    level NVARCHAR(20) NOT NULL,
                    module NVARCHAR(50) NULL,
                    message NVARCHAR(MAX) NOT NULL,
                    details NVARCHAR(MAX) NULL,
                    created_at DATETIME2 DEFAULT GETDATE() NOT NULL,
                    user_id INT NULL,
                    ip_address NVARCHAR(50) NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
                """)
                logger.info("系统日志表创建成功")
            else:
                logger.info("系统日志表已存在，跳过创建")

            # 更新角色权限，添加新的权限项
            logger.info("更新角色权限，添加新的权限项...")
            roles = db.engine.execute("SELECT id, name, permissions FROM roles").fetchall()
            for role in roles:
                role_id = role[0]
                role_name = role[1]
                permissions_json = role[2]

                try:
                    permissions = json.loads(permissions_json)

                    # 如果是系统管理员或管理员角色，添加新的权限项
                    if role_name in ['系统管理员', '管理员']:
                        # 已经有全局权限，不需要修改
                        if '*' in permissions and '*' in permissions['*']:
                            logger.info(f"角色 {role_name} 已有全局权限，无需更新")
                            continue

                        # 添加新的权限项
                        if 'setting' not in permissions:
                            permissions['setting'] = []

                        # 添加系统设置权限
                        for action in ['view', 'edit', 'super_delete', 'project_name', 'system_config']:
                            if action not in permissions['setting']:
                                permissions['setting'].append(action)

                        # 添加数据备份权限
                        permissions['backup'] = ['view', 'create', 'restore', 'delete', 'download']

                        # 添加系统监控权限
                        permissions['monitor'] = ['view', 'export']

                        # 更新权限
                        new_permissions_json = json.dumps(permissions)
                        db.engine.execute(
                            "UPDATE roles SET permissions = ? WHERE id = ?",
                            new_permissions_json, role_id
                        )
                        logger.info(f"角色 {role_name} 权限更新成功")

                    # 如果是超级管理员角色，添加查看权限
                    elif role_name == '超级管理员':
                        # 添加新的权限项
                        if 'setting' not in permissions:
                            permissions['setting'] = []

                        # 添加系统设置查看权限
                        if 'view' not in permissions['setting']:
                            permissions['setting'].append('view')

                        # 添加数据备份查看权限
                        permissions['backup'] = ['view']

                        # 添加系统监控查看权限
                        permissions['monitor'] = ['view']

                        # 更新权限
                        new_permissions_json = json.dumps(permissions)
                        db.engine.execute(
                            "UPDATE roles SET permissions = ? WHERE id = ?",
                            new_permissions_json, role_id
                        )
                        logger.info(f"角色 {role_name} 权限更新成功")

                except Exception as e:
                    logger.error(f"更新角色 {role_name} 权限时出错: {str(e)}")

            logger.info("系统设置相关表迁移完成")

        except Exception as e:
            logger.error(f"迁移过程中出错: {str(e)}")
            raise

if __name__ == "__main__":
    run_migration()
